Imports System
Imports System.Windows.Forms

Module Program
    ''' <summary>
    ''' نقطة الدخول الرئيسية للتطبيق
    ''' </summary>
    <STAThread>
    Sub Main(args As String())
        ' التحقق من وجود معامل لتشغيل الاختبارات
        If args.Length > 0 AndAlso args(0).ToLower() = "test" Then
            ' تشغيل الاختبارات في وضع وحدة التحكم
            Console.WriteLine("تشغيل اختبارات النظام...")
            CalculationTests.RunAllTests()
            Return
        End If

        ' تشغيل التطبيق العادي
        Application.EnableVisualStyles()
        Application.SetCompatibleTextRenderingDefault(False)
        Application.Run(New MainForm())
    End Sub
End Module
