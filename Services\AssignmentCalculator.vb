Imports System.Globalization

''' <summary>
''' خدمة حساب التكليفات - تحتوي على منطق حساب أيام التكليف والمبالغ
''' </summary>
Public Class AssignmentCalculator
    
    ''' <summary>
    ''' حساب عدد أيام التكليف مع استثناء العطل والجمع
    ''' </summary>
    Public Shared Function CalculateWorkingDays(startDate As Date, endDate As Date, Optional excludeFridays As Boolean = True, Optional excludeHolidays As Boolean = True) As Integer
        If endDate < startDate Then
            Return 0
        End If
        
        Dim totalDays As Integer = 0
        Dim currentDate As Date = startDate
        
        While currentDate <= endDate
            Dim includeDay As Boolean = True
            
            ' استثناء يوم الجمعة
            If excludeFridays AndAlso currentDate.DayOfWeek = DayOfWeek.Friday Then
                includeDay = False
            End If
            
            ' استثناء العطل الرسمية
            If excludeHolidays AndAlso IsPublicHoliday(currentDate) Then
                includeDay = False
            End If
            
            If includeDay Then
                totalDays += 1
            End If
            
            currentDate = currentDate.AddDays(1)
        End While
        
        Return totalDays
    End Function
    
    ''' <summary>
    ''' حساب إجمالي مبلغ التكليف
    ''' </summary>
    Public Shared Function CalculateAssignmentAmount(days As Integer, dailyRate As Decimal, Optional cityMultiplier As Decimal = 1.0D) As Decimal
        Return days * dailyRate * cityMultiplier
    End Function
    
    ''' <summary>
    ''' حساب قيمة الانتداب اليومي حسب المرتبة والمدينة
    ''' </summary>
    Public Shared Function GetDailyAllowanceRate(rank As EmployeeRank, city As String) As Decimal
        Dim baseRate As Decimal = GetBaseRateByRank(rank)
        Dim cityMultiplier As Decimal = GetCityMultiplier(city)
        
        Return baseRate * cityMultiplier
    End Function
    
    ''' <summary>
    ''' الحصول على المعدل الأساسي حسب المرتبة
    ''' </summary>
    Private Shared Function GetBaseRateByRank(rank As EmployeeRank) As Decimal
        Select Case rank
            Case EmployeeRank.Administrative
                Return 300D ' 300 ريال يومياً
            Case EmployeeRank.Supervisor
                Return 400D ' 400 ريال يومياً
            Case EmployeeRank.Manager
                Return 500D ' 500 ريال يومياً
            Case EmployeeRank.Director
                Return 600D ' 600 ريال يومياً
            Case Else
                Return 250D ' القيمة الافتراضية
        End Select
    End Function
    
    ''' <summary>
    ''' الحصول على معامل المدينة (بعض المدن لها بدل إضافي)
    ''' </summary>
    Private Shared Function GetCityMultiplier(city As String) As Decimal
        If String.IsNullOrWhiteSpace(city) Then
            Return 1.0D
        End If
        
        ' قائمة المدن ذات البدل الإضافي
        Dim highCostCities As New List(Of String) From {
            "الرياض", "جدة", "الدمام", "مكة المكرمة", "المدينة المنورة"
        }
        
        Dim remoteCities As New List(Of String) From {
            "تبوك", "أبها", "جازان", "نجران", "الباحة", "عرعر", "سكاكا"
        }
        
        If highCostCities.Contains(city) Then
            Return 1.2D ' زيادة 20% للمدن الكبرى
        ElseIf remoteCities.Contains(city) Then
            Return 1.3D ' زيادة 30% للمدن النائية
        Else
            Return 1.0D ' المعدل العادي
        End If
    End Function
    
    ''' <summary>
    ''' التحقق من كون التاريخ عطلة رسمية
    ''' </summary>
    Public Shared Function IsPublicHoliday(checkDate As Date) As Boolean
        ' العطل الرسمية الثابتة في المملكة العربية السعودية
        Dim fixedHolidays As New List(Of Date) From {
            New Date(checkDate.Year, 9, 23), ' اليوم الوطني
            New Date(checkDate.Year, 2, 22)  ' يوم التأسيس
        }
        
        ' العطل الرسمية المتغيرة (تحتاج تحديث سنوي)
        Dim variableHolidays As List(Of Date) = GetVariableHolidays(checkDate.Year)
        
        Return fixedHolidays.Contains(checkDate.Date) OrElse variableHolidays.Contains(checkDate.Date)
    End Function
    
    ''' <summary>
    ''' الحصول على العطل المتغيرة للسنة المحددة
    ''' </summary>
    Private Shared Function GetVariableHolidays(year As Integer) As List(Of Date)
        ' هذه تواريخ تقريبية - يجب تحديثها سنوياً حسب التقويم الهجري
        Dim holidays As New List(Of Date)
        
        Select Case year
            Case 2024
                holidays.AddRange({
                    New Date(2024, 4, 10), ' عيد الفطر - يوم 1
                    New Date(2024, 4, 11), ' عيد الفطر - يوم 2
                    New Date(2024, 4, 12), ' عيد الفطر - يوم 3
                    New Date(2024, 6, 16), ' عيد الأضحى - يوم 1
                    New Date(2024, 6, 17), ' عيد الأضحى - يوم 2
                    New Date(2024, 6, 18), ' عيد الأضحى - يوم 3
                    New Date(2024, 6, 19)  ' عيد الأضحى - يوم 4
                })
            Case 2025
                holidays.AddRange({
                    New Date(2025, 3, 30), ' عيد الفطر - يوم 1 (تقريبي)
                    New Date(2025, 3, 31), ' عيد الفطر - يوم 2
                    New Date(2025, 4, 1),  ' عيد الفطر - يوم 3
                    New Date(2025, 6, 6),  ' عيد الأضحى - يوم 1 (تقريبي)
                    New Date(2025, 6, 7),  ' عيد الأضحى - يوم 2
                    New Date(2025, 6, 8),  ' عيد الأضحى - يوم 3
                    New Date(2025, 6, 9)   ' عيد الأضحى - يوم 4
                })
            Case Else
                ' للسنوات الأخرى، يمكن إضافة منطق لحساب التواريخ أو تركها فارغة
        End Select
        
        Return holidays
    End Function
    
    ''' <summary>
    ''' حساب تفاصيل التكليف الكاملة
    ''' </summary>
    Public Shared Function CalculateAssignmentDetails(assignment As Assignment) As AssignmentCalculationResult
        Dim result As New AssignmentCalculationResult()
        
        ' حساب عدد الأيام
        result.TotalDays = CalculateWorkingDays(assignment.StartDate, assignment.EndDate)
        result.TotalCalendarDays = CInt((assignment.EndDate - assignment.StartDate).TotalDays) + 1
        result.ExcludedDays = result.TotalCalendarDays - result.TotalDays
        
        ' حساب المبالغ
        If assignment.Employee IsNot Nothing Then
            result.DailyRate = GetDailyAllowanceRate(assignment.Employee.Rank, assignment.Destination)
            result.TotalAmount = CalculateAssignmentAmount(result.TotalDays, result.DailyRate)
        Else
            result.DailyRate = assignment.DailyAllowance
            result.TotalAmount = CalculateAssignmentAmount(result.TotalDays, result.DailyRate)
        End If
        
        ' تفاصيل إضافية
        result.StartDate = assignment.StartDate
        result.EndDate = assignment.EndDate
        result.Destination = assignment.Destination
        result.IsInternalAssignment = assignment.IsInternalAssignment
        
        Return result
    End Function
    
    ''' <summary>
    ''' حساب إحصائيات التكليفات للموظف
    ''' </summary>
    Public Shared Function CalculateEmployeeAssignmentStats(employeeId As Integer, fromDate As Date, toDate As Date) As EmployeeAssignmentStats
        Dim dataManager As DataManager = DataManager.Instance
        Dim assignments = dataManager.GetAssignmentsByEmployee(employeeId).Where(Function(a) a.StartDate >= fromDate AndAlso a.EndDate <= toDate).ToList()
        
        Dim stats As New EmployeeAssignmentStats()
        stats.EmployeeId = employeeId
        stats.PeriodStart = fromDate
        stats.PeriodEnd = toDate
        stats.TotalAssignments = assignments.Count
        stats.TotalDays = assignments.Sum(Function(a) a.TotalDays)
        stats.TotalAmount = assignments.Sum(Function(a) a.TotalAmount)
        stats.InternalAssignments = assignments.Count(Function(a) a.IsInternalAssignment)
        stats.ExternalAssignments = assignments.Count(Function(a) Not a.IsInternalAssignment)
        
        Return stats
    End Function
End Class

''' <summary>
''' نتيجة حساب التكليف
''' </summary>
Public Class AssignmentCalculationResult
    Public Property StartDate As Date
    Public Property EndDate As Date
    Public Property Destination As String
    Public Property IsInternalAssignment As Boolean
    Public Property TotalCalendarDays As Integer
    Public Property TotalDays As Integer
    Public Property ExcludedDays As Integer
    Public Property DailyRate As Decimal
    Public Property TotalAmount As Decimal
    
    Public Overrides Function ToString() As String
        Return $"المدة: من {StartDate:yyyy/MM/dd} إلى {EndDate:yyyy/MM/dd}" & vbCrLf &
               $"إجمالي الأيام: {TotalCalendarDays} يوم" & vbCrLf &
               $"أيام العمل: {TotalDays} يوم" & vbCrLf &
               $"الأيام المستثناة: {ExcludedDays} يوم" & vbCrLf &
               $"قيمة الانتداب اليومي: {DailyRate:N2} ريال" & vbCrLf &
               $"إجمالي المبلغ: {TotalAmount:N2} ريال"
    End Function
End Class

''' <summary>
''' إحصائيات تكليفات الموظف
''' </summary>
Public Class EmployeeAssignmentStats
    Public Property EmployeeId As Integer
    Public Property PeriodStart As Date
    Public Property PeriodEnd As Date
    Public Property TotalAssignments As Integer
    Public Property TotalDays As Integer
    Public Property TotalAmount As Decimal
    Public Property InternalAssignments As Integer
    Public Property ExternalAssignments As Integer
    
    Public ReadOnly Property AverageDaysPerAssignment As Double
        Get
            If TotalAssignments = 0 Then Return 0
            Return TotalDays / TotalAssignments
        End Get
    End Property
    
    Public ReadOnly Property AverageAmountPerAssignment As Decimal
        Get
            If TotalAssignments = 0 Then Return 0
            Return TotalAmount / TotalAssignments
        End Get
    End Property
End Class
