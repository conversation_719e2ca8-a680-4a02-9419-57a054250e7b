Imports System.IO
Imports System.Text
'Imports OfficeOpenXml
'Imports iTextSharp.text
'Imports iTextSharp.text.pdf

''' <summary>
''' مولد التقارير - يتولى إنشاء التقارير بصيغ مختلفة
''' </summary>
Public Class ReportGenerator
    
    ''' <summary>
    ''' تصدير تقرير التكليفات إلى CSV (مؤقتاً بدلاً من Excel)
    ''' </summary>
    Public Shared Function ExportAssignmentsToExcel(assignments As List(Of Assignment), filePath As String) As Boolean
        Try
            ' تغيير امتداد الملف إلى CSV
            filePath = Path.ChangeExtension(filePath, ".csv")

            Using writer As New StreamWriter(filePath, False, Encoding.UTF8)
                ' كتابة الرؤوس
                writer.WriteLine("رقم التكليف,اسم الموظف,تاريخ البداية,تاريخ النهاية,الوجهة,الغرض,نوع التكليف,عدد الأيام,قيمة الانتداب اليومي,إجمالي المبلغ,ملاحظات")

                ' كتابة البيانات
                For Each assignment In assignments
                    Dim line = $"{assignment.AssignmentId}," &
                              $"""{If(assignment.Employee IsNot Nothing, assignment.Employee.Name, "")}""," &
                              $"{assignment.StartDate:yyyy/MM/dd}," &
                              $"{assignment.EndDate:yyyy/MM/dd}," &
                              $"""{assignment.Destination}""," &
                              $"""{assignment.Purpose}""," &
                              $"{If(assignment.IsInternalAssignment, "داخلي", "خارجي")}," &
                              $"{assignment.TotalDays}," &
                              $"{assignment.DailyAllowance:N2}," &
                              $"{assignment.TotalAmount:N2}," &
                              $"""{assignment.Notes}"""

                    writer.WriteLine(line)
                Next

                ' إضافة إجماليات
                writer.WriteLine()
                writer.WriteLine($"الإجماليات,,,,,,{assignments.Sum(Function(a) a.TotalDays)},,{assignments.Sum(Function(a) a.TotalAmount):N2},")
            End Using

            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' تصدير تقرير الإجازات إلى CSV (مؤقتاً بدلاً من Excel)
    ''' </summary>
    Public Shared Function ExportLeavesToExcel(leaves As List(Of Leave), filePath As String) As Boolean
        Try
            ' تغيير امتداد الملف إلى CSV
            filePath = Path.ChangeExtension(filePath, ".csv")

            Using writer As New StreamWriter(filePath, False, Encoding.UTF8)
                ' كتابة الرؤوس
                writer.WriteLine("رقم الإجازة,اسم الموظف,تاريخ البداية,تاريخ النهاية,نوع الإجازة,عدد الأيام,السبب,الحالة,معتمدة من,تاريخ الاعتماد,ملاحظات")

                ' كتابة البيانات
                For Each leave In leaves
                    Dim line = $"{leave.LeaveId}," &
                              $"""{If(leave.Employee IsNot Nothing, leave.Employee.Name, "")}""," &
                              $"{leave.StartDate:yyyy/MM/dd}," &
                              $"{leave.EndDate:yyyy/MM/dd}," &
                              $"{GetLeaveTypeName(leave.LeaveType)}," &
                              $"{leave.TotalDays}," &
                              $"""{leave.Reason}""," &
                              $"{If(leave.IsApproved, "معتمدة", "في الانتظار")}," &
                              $"""{leave.ApprovedBy}""," &
                              $"{If(leave.ApprovalDate.HasValue, leave.ApprovalDate.Value.ToString("yyyy/MM/dd"), "")}," &
                              $"""{leave.Notes}"""

                    writer.WriteLine(line)
                Next

                ' إضافة إحصائيات
                writer.WriteLine()
                writer.WriteLine($"إجمالي الأيام:,,,,{leaves.Sum(Function(l) l.TotalDays)}")
                writer.WriteLine($"الإجازات المعتمدة:,,,,{leaves.Count(Function(l) l.IsApproved)}")
                writer.WriteLine($"الإجازات في الانتظار:,,,,{leaves.Count(Function(l) Not l.IsApproved)}")
            End Using

            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' تصدير تقرير التكليفات إلى PDF (معطل مؤقتاً)
    ''' </summary>
    Public Shared Function ExportAssignmentsToPDF(assignments As List(Of Assignment), filePath As String) As Boolean
        ' وظيفة PDF معطلة مؤقتاً - يمكن تفعيلها بإضافة مكتبة iTextSharp
        Return False
    End Function
    
    ''' <summary>
    ''' إنشاء تقرير إحصائي للموظف (CSV مؤقتاً)
    ''' </summary>
    Public Shared Function GenerateEmployeeStatisticsReport(employee As Employee, fromDate As Date, toDate As Date, filePath As String) As Boolean
        Try
            ' حساب الإحصائيات
            Dim assignmentStats = AssignmentCalculator.CalculateEmployeeAssignmentStats(employee.EmployeeId, fromDate, toDate)
            Dim leaveStats = LeaveCalculator.CalculateEmployeeLeaveStats(employee.EmployeeId, fromDate, toDate)

            ' تغيير امتداد الملف إلى CSV
            filePath = Path.ChangeExtension(filePath, ".csv")

            Using writer As New StreamWriter(filePath, False, Encoding.UTF8)
                writer.WriteLine("تقرير إحصائي للموظف")
                writer.WriteLine()
                writer.WriteLine($"اسم الموظف,{employee.Name}")
                writer.WriteLine($"الوظيفة,{employee.Position}")
                writer.WriteLine($"القسم,{employee.Department}")
                writer.WriteLine($"فترة التقرير,من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}")
                writer.WriteLine()

                writer.WriteLine("إحصائيات التكليفات")
                writer.WriteLine($"عدد التكليفات,{assignmentStats.TotalAssignments}")
                writer.WriteLine($"إجمالي أيام التكليف,{assignmentStats.TotalDays}")
                writer.WriteLine($"إجمالي مبلغ التكليفات,{assignmentStats.TotalAmount:N2}")
                writer.WriteLine($"التكليفات الداخلية,{assignmentStats.InternalAssignments}")
                writer.WriteLine($"التكليفات الخارجية,{assignmentStats.ExternalAssignments}")
                writer.WriteLine()

                writer.WriteLine("إحصائيات الإجازات")
                writer.WriteLine($"عدد الإجازات,{leaveStats.TotalLeaves}")
                writer.WriteLine($"إجمالي أيام الإجازة,{leaveStats.TotalDays}")
                writer.WriteLine($"أيام الإجازة الاعتيادية,{leaveStats.AnnualLeaveDays}")
                writer.WriteLine($"أيام الإجازة المرضية,{leaveStats.SickLeaveDays}")
                writer.WriteLine($"أيام الإجازة الاضطرارية,{leaveStats.EmergencyLeaveDays}")
                writer.WriteLine()

                writer.WriteLine("أرصدة الإجازات الحالية")
                writer.WriteLine($"رصيد الإجازة الاعتيادية,{employee.AnnualLeaveBalance}")
                writer.WriteLine($"رصيد الإجازة المرضية,{employee.SickLeaveBalance}")
                writer.WriteLine($"رصيد الإجازة الاضطرارية,{employee.EmergencyLeaveBalance}")
            End Using

            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' الحصول على اسم نوع الإجازة باللغة العربية
    ''' </summary>
    Private Shared Function GetLeaveTypeName(leaveType As LeaveType) As String
        Select Case leaveType
            Case LeaveType.Annual
                Return "اعتيادية"
            Case LeaveType.Sick
                Return "مرضية"
            Case LeaveType.Emergency
                Return "اضطرارية"
            Case LeaveType.Maternity
                Return "أمومة"
            Case LeaveType.Paternity
                Return "أبوة"
            Case LeaveType.Hajj
                Return "حج"
            Case Else
                Return "أخرى"
        End Select
    End Function
End Class
