Imports System.IO
Imports System.Text.Json

''' <summary>
''' مدير البيانات - يتولى حفظ واسترجاع البيانات
''' </summary>
Public Class DataManager
    Private Shared _instance As DataManager
    Private _employees As List(Of Employee)
    Private _assignments As List(Of Assignment)
    Private _leaves As List(Of Leave)
    Private ReadOnly _dataFilePath As String = "data.json"
    
    Private Sub New()
        _employees = New List(Of Employee)
        _assignments = New List(Of Assignment)
        _leaves = New List(Of Leave)
        LoadData()
    End Sub
    
    Public Shared ReadOnly Property Instance As DataManager
        Get
            If _instance Is Nothing Then
                _instance = New DataManager()
            End If
            Return _instance
        End Get
    End Property
    
    ' خصائص للوصول إلى البيانات
    Public ReadOnly Property Employees As List(Of Employee)
        Get
            Return _employees
        End Get
    End Property
    
    Public ReadOnly Property Assignments As List(Of Assignment)
        Get
            Return _assignments
        End Get
    End Property
    
    Public ReadOnly Property Leaves As List(Of Leave)
        Get
            Return _leaves
        End Get
    End Property
    
    ' إدارة الموظفين
    Public Function AddEmployee(employee As Employee) As Boolean
        Try
            If _employees.Any(Function(e) e.EmployeeId = employee.EmployeeId) Then
                Return False ' الموظف موجود مسبقاً
            End If
            
            _employees.Add(employee)
            SaveData()
            Return True
        Catch
            Return False
        End Try
    End Function
    
    Public Function GetEmployee(employeeId As Integer) As Employee
        Return _employees.FirstOrDefault(Function(e) e.EmployeeId = employeeId)
    End Function
    
    Public Function UpdateEmployee(employee As Employee) As Boolean
        Try
            Dim existingEmployee = GetEmployee(employee.EmployeeId)
            If existingEmployee IsNot Nothing Then
                Dim index = _employees.IndexOf(existingEmployee)
                _employees(index) = employee
                SaveData()
                Return True
            End If
            Return False
        Catch
            Return False
        End Try
    End Function
    
    ' إدارة التكليفات
    Public Function AddAssignment(assignment As Assignment) As Boolean
        Try
            assignment.AssignmentId = GetNextAssignmentId()
            assignment.Employee = GetEmployee(assignment.EmployeeId)
            _assignments.Add(assignment)
            SaveData()
            Return True
        Catch
            Return False
        End Try
    End Function
    
    Public Function GetAssignmentsByEmployee(employeeId As Integer) As List(Of Assignment)
        Return _assignments.Where(Function(a) a.EmployeeId = employeeId).ToList()
    End Function
    
    ' إدارة الإجازات
    Public Function AddLeave(leave As Leave) As Boolean
        Try
            leave.LeaveId = GetNextLeaveId()
            leave.Employee = GetEmployee(leave.EmployeeId)
            
            ' التحقق من الرصيد المتاح
            If leave.CanGrantLeave() Then
                _leaves.Add(leave)
                SaveData()
                Return True
            End If
            Return False
        Catch
            Return False
        End Try
    End Function
    
    Public Function ApproveLeave(leaveId As Integer, approvedBy As String) As Boolean
        Try
            Dim leave = _leaves.FirstOrDefault(Function(l) l.LeaveId = leaveId)
            If leave IsNot Nothing AndAlso Not leave.IsApproved Then
                leave.IsApproved = True
                leave.ApprovedBy = approvedBy
                leave.ApprovalDate = Date.Now
                leave.DeductFromBalance()
                SaveData()
                Return True
            End If
            Return False
        Catch
            Return False
        End Try
    End Function
    
    Public Function GetLeavesByEmployee(employeeId As Integer) As List(Of Leave)
        Return _leaves.Where(Function(l) l.EmployeeId = employeeId).ToList()
    End Function
    
    ' وظائف مساعدة
    Private Function GetNextAssignmentId() As Integer
        If _assignments.Count = 0 Then
            Return 1
        End If
        Return _assignments.Max(Function(a) a.AssignmentId) + 1
    End Function
    
    Private Function GetNextLeaveId() As Integer
        If _leaves.Count = 0 Then
            Return 1
        End If
        Return _leaves.Max(Function(l) l.LeaveId) + 1
    End Function
    
    ' حفظ واسترجاع البيانات
    Private Sub SaveData()
        Try
            Dim data = New With {
                .Employees = _employees,
                .Assignments = _assignments,
                .Leaves = _leaves
            }
            
            Dim jsonString = JsonSerializer.Serialize(data, New JsonSerializerOptions With {.WriteIndented = True})
            File.WriteAllText(_dataFilePath, jsonString)
        Catch ex As Exception
            ' يمكن إضافة تسجيل الأخطاء هنا
        End Try
    End Sub
    
    Private Sub LoadData()
        Try
            If File.Exists(_dataFilePath) Then
                Dim jsonString = File.ReadAllText(_dataFilePath)
                ' يمكن تحسين عملية تحميل البيانات من JSON هنا
                ' للبساطة، سنبدأ ببيانات تجريبية
            End If
            
            ' إضافة بيانات تجريبية إذا لم تكن هناك بيانات محفوظة
            If _employees.Count = 0 Then
                LoadSampleData()
            End If
        Catch ex As Exception
            LoadSampleData()
        End Try
    End Sub
    
    Private Sub LoadSampleData()
        ' إضافة موظفين تجريبيين
        _employees.Add(New Employee(1, "أحمد محمد", "محاسب", "المالية", EmployeeRank.Administrative))
        _employees.Add(New Employee(2, "فاطمة علي", "مشرفة", "الموارد البشرية", EmployeeRank.Supervisor))
        _employees.Add(New Employee(3, "محمد سالم", "مدير", "تقنية المعلومات", EmployeeRank.Manager))
    End Sub
End Class
