''' <summary>
''' خدمة حساب الإجازات - تحتوي على منطق حساب أيام الإجازة وإدارة الأرصدة
''' </summary>
Public Class LeaveCalculator
    
    ''' <summary>
    ''' حساب عدد أيام الإجازة حسب نوعها
    ''' </summary>
    Public Shared Function CalculateLeaveDays(startDate As Date, endDate As Date, leaveType As LeaveType) As Integer
        If endDate < startDate Then
            Return 0
        End If
        
        Dim totalDays As Integer = 0
        Dim currentDate As Date = startDate
        
        While currentDate <= endDate
            Dim includeDay As Boolean = True
            
            ' منطق مختلف حسب نوع الإجازة
            Select Case leaveType
                Case LeaveType.Annual
                    ' الإجازة الاعتيادية: استثناء الجمع والعطل الرسمية
                    If currentDate.DayOfWeek = DayOfWeek.Friday OrElse AssignmentCalculator.IsPublicHoliday(currentDate) Then
                        includeDay = False
                    End If
                    
                Case LeaveType.Sick
                    ' الإجازة المرضية: تحسب جميع الأيام
                    includeDay = True
                    
                Case LeaveType.Emergency
                    ' الإجازة الاضطرارية: تحسب جميع الأيام
                    includeDay = True
                    
                Case LeaveType.Maternity
                    ' إجازة الأمومة: تحسب جميع الأيام
                    includeDay = True
                    
                Case LeaveType.Paternity
                    ' إجازة الأبوة: استثناء الجمع والعطل الرسمية
                    If currentDate.DayOfWeek = DayOfWeek.Friday OrElse AssignmentCalculator.IsPublicHoliday(currentDate) Then
                        includeDay = False
                    End If
                    
                Case LeaveType.Hajj
                    ' إجازة الحج: تحسب جميع الأيام
                    includeDay = True
                    
                Case Else
                    includeDay = True
            End Select
            
            If includeDay Then
                totalDays += 1
            End If
            
            currentDate = currentDate.AddDays(1)
        End While
        
        Return totalDays
    End Function
    
    ''' <summary>
    ''' التحقق من إمكانية منح الإجازة حسب الرصيد المتاح
    ''' </summary>
    Public Shared Function CanGrantLeave(employee As Employee, leaveType As LeaveType, requestedDays As Integer) As LeaveValidationResult
        Dim result As New LeaveValidationResult()
        result.LeaveType = leaveType
        result.RequestedDays = requestedDays
        result.IsValid = True
        result.Messages = New List(Of String)
        
        If employee Is Nothing Then
            result.IsValid = False
            result.Messages.Add("بيانات الموظف غير متوفرة")
            Return result
        End If
        
        Select Case leaveType
            Case LeaveType.Annual
                result.AvailableBalance = employee.AnnualLeaveBalance
                If employee.AnnualLeaveBalance < requestedDays Then
                    result.IsValid = False
                    result.Messages.Add($"رصيد الإجازة الاعتيادية غير كافي. المتاح: {employee.AnnualLeaveBalance} يوم")
                End If
                
            Case LeaveType.Sick
                result.AvailableBalance = employee.SickLeaveBalance
                If employee.SickLeaveBalance < requestedDays Then
                    result.IsValid = False
                    result.Messages.Add($"رصيد الإجازة المرضية غير كافي. المتاح: {employee.SickLeaveBalance} يوم")
                End If
                
            Case LeaveType.Emergency
                result.AvailableBalance = employee.EmergencyLeaveBalance
                If employee.EmergencyLeaveBalance < requestedDays Then
                    result.IsValid = False
                    result.Messages.Add($"رصيد الإجازة الاضطرارية غير كافي. المتاح: {employee.EmergencyLeaveBalance} يوم")
                End If
                
            Case LeaveType.Maternity
                ' إجازة الأمومة: 10 أسابيع (70 يوم)
                result.AvailableBalance = 70
                If requestedDays > 70 Then
                    result.IsValid = False
                    result.Messages.Add("إجازة الأمومة لا تتجاوز 70 يوماً")
                End If
                
                ' التحقق من عدم وجود إجازة أمومة أخرى في نفس السنة
                If HasMaternityLeaveThisYear(employee.EmployeeId) Then
                    result.IsValid = False
                    result.Messages.Add("لا يمكن الحصول على أكثر من إجازة أمومة واحدة في السنة")
                End If
                
            Case LeaveType.Paternity
                ' إجازة الأبوة: 3 أيام
                result.AvailableBalance = 3
                If requestedDays > 3 Then
                    result.IsValid = False
                    result.Messages.Add("إجازة الأبوة لا تتجاوز 3 أيام")
                End If
                
            Case LeaveType.Hajj
                ' إجازة الحج: مرة واحدة في الخدمة
                result.AvailableBalance = If(HasHajjLeave(employee.EmployeeId), 0, 21)
                If HasHajjLeave(employee.EmployeeId) Then
                    result.IsValid = False
                    result.Messages.Add("لا يمكن الحصول على أكثر من إجازة حج واحدة في الخدمة")
                ElseIf requestedDays > 21 Then
                    result.IsValid = False
                    result.Messages.Add("إجازة الحج لا تتجاوز 21 يوماً")
                End If
        End Select
        
        Return result
    End Function
    
    ''' <summary>
    ''' خصم أيام الإجازة من رصيد الموظف
    ''' </summary>
    Public Shared Function DeductLeaveFromBalance(employee As Employee, leaveType As LeaveType, days As Integer) As Boolean
        Try
            Select Case leaveType
                Case LeaveType.Annual
                    If employee.AnnualLeaveBalance >= days Then
                        employee.AnnualLeaveBalance -= days
                        Return True
                    End If
                    
                Case LeaveType.Sick
                    If employee.SickLeaveBalance >= days Then
                        employee.SickLeaveBalance -= days
                        Return True
                    End If
                    
                Case LeaveType.Emergency
                    If employee.EmergencyLeaveBalance >= days Then
                        employee.EmergencyLeaveBalance -= days
                        Return True
                    End If
                    
                Case LeaveType.Maternity, LeaveType.Paternity, LeaveType.Hajj
                    ' هذه الإجازات لا تخصم من الرصيد العادي
                    Return True
            End Select
            
            Return False
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' إرجاع أيام الإجازة إلى رصيد الموظف (في حالة الإلغاء)
    ''' </summary>
    Public Shared Function RestoreLeaveToBalance(employee As Employee, leaveType As LeaveType, days As Integer) As Boolean
        Try
            Select Case leaveType
                Case LeaveType.Annual
                    employee.AnnualLeaveBalance += days
                    Return True
                    
                Case LeaveType.Sick
                    employee.SickLeaveBalance += days
                    Return True
                    
                Case LeaveType.Emergency
                    employee.EmergencyLeaveBalance += days
                    Return True
                    
                Case LeaveType.Maternity, LeaveType.Paternity, LeaveType.Hajj
                    ' هذه الإجازات لا تؤثر على الرصيد العادي
                    Return True
            End Select
            
            Return False
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' حساب تفاصيل الإجازة الكاملة
    ''' </summary>
    Public Shared Function CalculateLeaveDetails(leave As Leave) As LeaveCalculationResult
        Dim result As New LeaveCalculationResult()
        
        ' حساب عدد الأيام
        result.TotalDays = CalculateLeaveDays(leave.StartDate, leave.EndDate, leave.LeaveType)
        result.TotalCalendarDays = CInt((leave.EndDate - leave.StartDate).TotalDays) + 1
        result.ExcludedDays = result.TotalCalendarDays - result.TotalDays
        
        ' التحقق من صحة الإجازة
        If leave.Employee IsNot Nothing Then
            result.ValidationResult = CanGrantLeave(leave.Employee, leave.LeaveType, result.TotalDays)
        End If
        
        ' تفاصيل إضافية
        result.StartDate = leave.StartDate
        result.EndDate = leave.EndDate
        result.LeaveType = leave.LeaveType
        result.Reason = leave.Reason
        
        Return result
    End Function
    
    ''' <summary>
    ''' حساب إحصائيات الإجازات للموظف
    ''' </summary>
    Public Shared Function CalculateEmployeeLeaveStats(employeeId As Integer, fromDate As Date, toDate As Date) As EmployeeLeaveStats
        Dim dataManager As DataManager = DataManager.Instance
        Dim leaves = dataManager.GetLeavesByEmployee(employeeId).Where(Function(l) l.StartDate >= fromDate AndAlso l.EndDate <= toDate AndAlso l.IsApproved).ToList()
        
        Dim stats As New EmployeeLeaveStats()
        stats.EmployeeId = employeeId
        stats.PeriodStart = fromDate
        stats.PeriodEnd = toDate
        stats.TotalLeaves = leaves.Count
        stats.TotalDays = leaves.Sum(Function(l) l.TotalDays)
        stats.AnnualLeaveDays = leaves.Where(Function(l) l.LeaveType = LeaveType.Annual).Sum(Function(l) l.TotalDays)
        stats.SickLeaveDays = leaves.Where(Function(l) l.LeaveType = LeaveType.Sick).Sum(Function(l) l.TotalDays)
        stats.EmergencyLeaveDays = leaves.Where(Function(l) l.LeaveType = LeaveType.Emergency).Sum(Function(l) l.TotalDays)
        stats.OtherLeaveDays = leaves.Where(Function(l) l.LeaveType > LeaveType.Emergency).Sum(Function(l) l.TotalDays)
        
        Return stats
    End Function
    
    ''' <summary>
    ''' تجديد أرصدة الإجازات السنوية
    ''' </summary>
    Public Shared Sub RenewAnnualLeaveBalances()
        Dim dataManager As DataManager = DataManager.Instance
        
        For Each employee In dataManager.Employees
            ' تجديد رصيد الإجازة الاعتيادية
            employee.AnnualLeaveBalance = 30
            
            ' تجديد رصيد الإجازة المرضية
            employee.SickLeaveBalance = 30
            
            ' تجديد رصيد الإجازة الاضطرارية
            employee.EmergencyLeaveBalance = 5
        Next
    End Sub
    
    ''' <summary>
    ''' التحقق من وجود إجازة أمومة في السنة الحالية
    ''' </summary>
    Private Shared Function HasMaternityLeaveThisYear(employeeId As Integer) As Boolean
        Dim dataManager As DataManager = DataManager.Instance
        Dim currentYear = Date.Now.Year
        
        Return dataManager.GetLeavesByEmployee(employeeId).Any(Function(l) l.LeaveType = LeaveType.Maternity AndAlso l.StartDate.Year = currentYear AndAlso l.IsApproved)
    End Function
    
    ''' <summary>
    ''' التحقق من وجود إجازة حج سابقة
    ''' </summary>
    Private Shared Function HasHajjLeave(employeeId As Integer) As Boolean
        Dim dataManager As DataManager = DataManager.Instance
        
        Return dataManager.GetLeavesByEmployee(employeeId).Any(Function(l) l.LeaveType = LeaveType.Hajj AndAlso l.IsApproved)
    End Function
End Class

''' <summary>
''' نتيجة التحقق من صحة الإجازة
''' </summary>
Public Class LeaveValidationResult
    Public Property LeaveType As LeaveType
    Public Property RequestedDays As Integer
    Public Property AvailableBalance As Integer
    Public Property IsValid As Boolean
    Public Property Messages As List(Of String)
    
    Public Sub New()
        Messages = New List(Of String)
    End Sub
End Class

''' <summary>
''' نتيجة حساب الإجازة
''' </summary>
Public Class LeaveCalculationResult
    Public Property StartDate As Date
    Public Property EndDate As Date
    Public Property LeaveType As LeaveType
    Public Property Reason As String
    Public Property TotalCalendarDays As Integer
    Public Property TotalDays As Integer
    Public Property ExcludedDays As Integer
    Public Property ValidationResult As LeaveValidationResult
    
    Public Overrides Function ToString() As String
        Dim result As String = $"نوع الإجازة: {LeaveType}" & vbCrLf &
                              $"المدة: من {StartDate:yyyy/MM/dd} إلى {EndDate:yyyy/MM/dd}" & vbCrLf &
                              $"إجمالي الأيام: {TotalCalendarDays} يوم" & vbCrLf &
                              $"أيام الإجازة الفعلية: {TotalDays} يوم" & vbCrLf &
                              $"الأيام المستثناة: {ExcludedDays} يوم"
        
        If ValidationResult IsNot Nothing Then
            result &= vbCrLf & $"الرصيد المتاح: {ValidationResult.AvailableBalance} يوم"
            result &= vbCrLf & $"حالة الطلب: {If(ValidationResult.IsValid, "صالح", "غير صالح")}"
            
            If ValidationResult.Messages.Count > 0 Then
                result &= vbCrLf & "ملاحظات: " & String.Join(", ", ValidationResult.Messages)
            End If
        End If
        
        Return result
    End Function
End Class

''' <summary>
''' إحصائيات إجازات الموظف
''' </summary>
Public Class EmployeeLeaveStats
    Public Property EmployeeId As Integer
    Public Property PeriodStart As Date
    Public Property PeriodEnd As Date
    Public Property TotalLeaves As Integer
    Public Property TotalDays As Integer
    Public Property AnnualLeaveDays As Integer
    Public Property SickLeaveDays As Integer
    Public Property EmergencyLeaveDays As Integer
    Public Property OtherLeaveDays As Integer
    
    Public ReadOnly Property AverageDaysPerLeave As Double
        Get
            If TotalLeaves = 0 Then Return 0
            Return TotalDays / TotalLeaves
        End Get
    End Property
End Class
