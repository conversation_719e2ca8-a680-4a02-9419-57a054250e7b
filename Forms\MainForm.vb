Imports System
Imports System.Windows.Forms
Imports System.Drawing

Public Class MainForm
    Inherits Form
    Private _dataManager As DataManager
    Private _selectedEmployee As Employee
    
    Public Sub New()
        ' هذا الاستدعاء مطلوب من قبل مصمم النماذج
        InitializeComponent()
        
        ' إضافة أي تهيئة بعد استدعاء InitializeComponent()
        _dataManager = DataManager.Instance
        
        ' تعيين اتجاه النموذج من اليمين إلى اليسار
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        
        ' تعيين عنوان النموذج
        Me.Text = "نظام حسابات الموظفين في المهام الرسمية"
    End Sub
    
    Private Sub MainForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تحميل بيانات الموظفين في القائمة المنسدلة
        LoadEmployees()
        
        ' تعيين التاريخ الحالي في حقول التاريخ
        dtpAssignmentStart.Value = Date.Today
        dtpAssignmentEnd.Value = Date.Today.AddDays(1)
        dtpLeaveStart.Value = Date.Today
        dtpLeaveEnd.Value = Date.Today.AddDays(1)
        
        ' تحميل أنواع الإجازات في القائمة المنسدلة
        LoadLeaveTypes()
    End Sub
    
    Private Sub LoadEmployees()
        cboEmployees.DataSource = Nothing
        cboEmployees.DisplayMember = "Name"
        cboEmployees.ValueMember = "EmployeeId"
        cboEmployees.DataSource = _dataManager.Employees
    End Sub
    
    Private Sub LoadLeaveTypes()
        cboLeaveType.DataSource = [Enum].GetValues(GetType(LeaveType))
    End Sub
    
    Private Sub cboEmployees_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cboEmployees.SelectedIndexChanged
        If cboEmployees.SelectedItem IsNot Nothing Then
            _selectedEmployee = DirectCast(cboEmployees.SelectedItem, Employee)
            DisplayEmployeeInfo()
            LoadEmployeeAssignments()
            LoadEmployeeLeaves()
        End If
    End Sub
    
    Private Sub DisplayEmployeeInfo()
        If _selectedEmployee IsNot Nothing Then
            lblEmployeeInfo.Text = $"الاسم: {_selectedEmployee.Name}" & vbCrLf & 
                                  $"الوظيفة: {_selectedEmployee.Position}" & vbCrLf & 
                                  $"القسم: {_selectedEmployee.Department}" & vbCrLf & 
                                  $"المرتبة: {_selectedEmployee.Rank}" & vbCrLf & 
                                  $"قيمة الانتداب اليومي: {_selectedEmployee.DailyAllowanceRate} ريال"
            
            lblLeaveBalance.Text = $"رصيد الإجازة الاعتيادية: {_selectedEmployee.AnnualLeaveBalance} يوم" & vbCrLf & 
                                  $"رصيد الإجازة المرضية: {_selectedEmployee.SickLeaveBalance} يوم" & vbCrLf & 
                                  $"رصيد الإجازة الاضطرارية: {_selectedEmployee.EmergencyLeaveBalance} يوم"
        End If
    End Sub
    
    Private Sub LoadEmployeeAssignments()
        If _selectedEmployee IsNot Nothing Then
            Dim assignments = _dataManager.GetAssignmentsByEmployee(_selectedEmployee.EmployeeId)
            dgvAssignments.DataSource = assignments
            
            ' تنسيق عرض الجدول
            FormatAssignmentsGrid()
        End If
    End Sub
    
    Private Sub LoadEmployeeLeaves()
        If _selectedEmployee IsNot Nothing Then
            Dim leaves = _dataManager.GetLeavesByEmployee(_selectedEmployee.EmployeeId)
            dgvLeaves.DataSource = leaves
            
            ' تنسيق عرض الجدول
            FormatLeavesGrid()
        End If
    End Sub
    
    Private Sub FormatAssignmentsGrid()
        ' تنسيق جدول التكليفات
        With dgvAssignments
            .Columns("AssignmentId").HeaderText = "رقم التكليف"
            .Columns("EmployeeId").Visible = False
            .Columns("Employee").Visible = False
            .Columns("StartDate").HeaderText = "تاريخ البداية"
            .Columns("EndDate").HeaderText = "تاريخ النهاية"
            .Columns("Destination").HeaderText = "الوجهة"
            .Columns("Purpose").HeaderText = "الغرض"
            .Columns("IsInternalAssignment").HeaderText = "داخلي"
            .Columns("DailyAllowance").HeaderText = "قيمة الانتداب اليومي"
            .Columns("TotalDays").HeaderText = "عدد الأيام"
            .Columns("TotalAmount").HeaderText = "المبلغ الإجمالي"
            .Columns("Notes").HeaderText = "ملاحظات"
            .Columns("CreatedDate").HeaderText = "تاريخ الإنشاء"
        End With
    End Sub
    
    Private Sub FormatLeavesGrid()
        ' تنسيق جدول الإجازات
        With dgvLeaves
            .Columns("LeaveId").HeaderText = "رقم الإجازة"
            .Columns("EmployeeId").Visible = False
            .Columns("Employee").Visible = False
            .Columns("StartDate").HeaderText = "تاريخ البداية"
            .Columns("EndDate").HeaderText = "تاريخ النهاية"
            .Columns("LeaveType").HeaderText = "نوع الإجازة"
            .Columns("TotalDays").HeaderText = "عدد الأيام"
            .Columns("Reason").HeaderText = "السبب"
            .Columns("IsApproved").HeaderText = "معتمدة"
            .Columns("ApprovedBy").HeaderText = "معتمدة من"
            .Columns("ApprovalDate").HeaderText = "تاريخ الاعتماد"
            .Columns("Notes").HeaderText = "ملاحظات"
            .Columns("CreatedDate").HeaderText = "تاريخ الإنشاء"
        End With
    End Sub
    
    Private Sub btnCalculateAssignment_Click(sender As Object, e As EventArgs) Handles btnCalculateAssignment.Click
        If _selectedEmployee Is Nothing Then
            MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        If dtpAssignmentEnd.Value < dtpAssignmentStart.Value Then
            MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        
        Dim assignment As New Assignment(_selectedEmployee.EmployeeId, dtpAssignmentStart.Value, dtpAssignmentEnd.Value, txtDestination.Text)
        assignment.Purpose = txtPurpose.Text
        assignment.IsInternalAssignment = chkInternalAssignment.Checked
        assignment.Employee = _selectedEmployee
        
        Dim days = assignment.CalculateAssignmentDays()
        Dim amount = assignment.CalculateTotalAmount()
        
        lblAssignmentResult.Text = $"عدد أيام التكليف: {days} يوم" & vbCrLf & 
                                  $"قيمة الانتداب اليومي: {_selectedEmployee.DailyAllowanceRate} ريال" & vbCrLf & 
                                  $"إجمالي المبلغ: {amount} ريال"
    End Sub
    
    Private Sub btnSaveAssignment_Click(sender As Object, e As EventArgs) Handles btnSaveAssignment.Click
        If _selectedEmployee Is Nothing Then
            MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        If String.IsNullOrWhiteSpace(txtDestination.Text) Then
            MessageBox.Show("الرجاء إدخال وجهة التكليف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        If dtpAssignmentEnd.Value < dtpAssignmentStart.Value Then
            MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        
        Dim assignment As New Assignment(_selectedEmployee.EmployeeId, dtpAssignmentStart.Value, dtpAssignmentEnd.Value, txtDestination.Text)
        assignment.Purpose = txtPurpose.Text
        assignment.IsInternalAssignment = chkInternalAssignment.Checked
        assignment.Notes = txtAssignmentNotes.Text
        
        ' حساب عدد الأيام والمبلغ الإجمالي
        assignment.CalculateAssignmentDays()
        assignment.CalculateTotalAmount()
        
        If _dataManager.AddAssignment(assignment) Then
            MessageBox.Show("تم حفظ التكليف بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadEmployeeAssignments()
            ClearAssignmentFields()
        Else
            MessageBox.Show("حدث خطأ أثناء حفظ التكليف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    
    Private Sub btnCalculateLeave_Click(sender As Object, e As EventArgs) Handles btnCalculateLeave.Click
        If _selectedEmployee Is Nothing Then
            MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        If dtpLeaveEnd.Value < dtpLeaveStart.Value Then
            MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        
        Dim leaveType As LeaveType = DirectCast(cboLeaveType.SelectedItem, LeaveType)
        Dim leave As New Leave(_selectedEmployee.EmployeeId, dtpLeaveStart.Value, dtpLeaveEnd.Value, leaveType)
        leave.Employee = _selectedEmployee
        
        Dim days = leave.CalculateLeaveDays()
        Dim canGrant = leave.CanGrantLeave()
        
        lblLeaveResult.Text = $"عدد أيام الإجازة: {days} يوم" & vbCrLf & 
                             $"نوع الإجازة: {leaveType}" & vbCrLf & 
                             $"الرصيد المتاح: {GetAvailableBalance(leaveType)} يوم" & vbCrLf & 
                             $"إمكانية منح الإجازة: {If(canGrant, "متاح", "غير متاح")}"
    End Sub
    
    Private Function GetAvailableBalance(leaveType As LeaveType) As Integer
        If _selectedEmployee Is Nothing Then
            Return 0
        End If
        
        Select Case leaveType
            Case LeaveType.Annual
                Return _selectedEmployee.AnnualLeaveBalance
            Case LeaveType.Sick
                Return _selectedEmployee.SickLeaveBalance
            Case LeaveType.Emergency
                Return _selectedEmployee.EmergencyLeaveBalance
            Case Else
                Return 0
        End Select
    End Function
    
    Private Sub btnSaveLeave_Click(sender As Object, e As EventArgs) Handles btnSaveLeave.Click
        If _selectedEmployee Is Nothing Then
            MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        If dtpLeaveEnd.Value < dtpLeaveStart.Value Then
            MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        
        Dim leaveType As LeaveType = DirectCast(cboLeaveType.SelectedItem, LeaveType)
        Dim leave As New Leave(_selectedEmployee.EmployeeId, dtpLeaveStart.Value, dtpLeaveEnd.Value, leaveType)
        leave.Reason = txtLeaveReason.Text
        leave.Notes = txtLeaveNotes.Text
        leave.Employee = _selectedEmployee
        
        ' التحقق من إمكانية منح الإجازة
        If Not leave.CanGrantLeave() Then
            MessageBox.Show("لا يمكن منح الإجازة بسبب عدم كفاية الرصيد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        If _dataManager.AddLeave(leave) Then
            MessageBox.Show("تم حفظ الإجازة بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadEmployeeLeaves()
            ClearLeaveFields()
        Else
            MessageBox.Show("حدث خطأ أثناء حفظ الإجازة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    
    Private Sub ClearAssignmentFields()
        txtDestination.Clear()
        txtPurpose.Clear()
        txtAssignmentNotes.Clear()
        dtpAssignmentStart.Value = Date.Today
        dtpAssignmentEnd.Value = Date.Today.AddDays(1)
        chkInternalAssignment.Checked = True
        lblAssignmentResult.Text = ""
    End Sub
    
    Private Sub ClearLeaveFields()
        txtLeaveReason.Clear()
        txtLeaveNotes.Clear()
        dtpLeaveStart.Value = Date.Today
        dtpLeaveEnd.Value = Date.Today.AddDays(1)
        lblLeaveResult.Text = ""
    End Sub
    
    Private Sub btnExportAssignments_Click(sender As Object, e As EventArgs) Handles btnExportAssignments.Click
        ' تصدير التكليفات إلى ملف Excel
        If dgvAssignments.Rows.Count = 0 Then
            MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        ' هنا يمكن إضافة كود لتصدير البيانات إلى Excel
        MessageBox.Show("سيتم تنفيذ هذه الميزة في الإصدار القادم", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub btnExportLeaves_Click(sender As Object, e As EventArgs) Handles btnExportLeaves.Click
        ' تصدير الإجازات إلى ملف Excel
        If dgvLeaves.Rows.Count = 0 Then
            MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        ' هنا يمكن إضافة كود لتصدير البيانات إلى Excel
        MessageBox.Show("سيتم تنفيذ هذه الميزة في الإصدار القادم", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
End Class
