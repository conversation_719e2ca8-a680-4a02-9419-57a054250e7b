''' <summary>
''' اختبارات وحدة الحسابات - للتأكد من صحة حسابات التكليفات والإجازات
''' </summary>
Public Class CalculationTests
    
    ''' <summary>
    ''' اختبار حساب أيام التكليف
    ''' </summary>
    Public Shared Sub TestAssignmentDaysCalculation()
        Console.WriteLine("=== اختبار حساب أيام التكليف ===")
        
        ' اختبار 1: تكليف لمدة أسبوع (5 أيام عمل)
        Dim startDate1 As New Date(2024, 1, 1) ' الاثنين
        Dim endDate1 As New Date(2024, 1, 5)   ' الجمعة
        Dim days1 = AssignmentCalculator.CalculateWorkingDays(startDate1, endDate1)
        Console.WriteLine($"اختبار 1: من {startDate1:yyyy/MM/dd} إلى {endDate1:yyyy/MM/dd}")
        Console.WriteLine($"النتيجة المتوقعة: 4 أيام (استثناء الجمعة)")
        Console.WriteLine($"النتيجة الفعلية: {days1} أيام")
        Console.WriteLine($"النتيجة: {If(days1 = 4, "نجح", "فشل")}")
        Console.WriteLine()
        
        ' اختبار 2: تكليف لمدة شهر
        Dim startDate2 As New Date(2024, 1, 1)
        Dim endDate2 As New Date(2024, 1, 31)
        Dim days2 = AssignmentCalculator.CalculateWorkingDays(startDate2, endDate2)
        Console.WriteLine($"اختبار 2: من {startDate2:yyyy/MM/dd} إلى {endDate2:yyyy/MM/dd}")
        Console.WriteLine($"النتيجة الفعلية: {days2} أيام")
        Console.WriteLine()
        
        ' اختبار 3: تكليف يشمل عطلة رسمية
        Dim startDate3 As New Date(2024, 9, 20)
        Dim endDate3 As New Date(2024, 9, 25)
        Dim days3 = AssignmentCalculator.CalculateWorkingDays(startDate3, endDate3)
        Console.WriteLine($"اختبار 3: من {startDate3:yyyy/MM/dd} إلى {endDate3:yyyy/MM/dd} (يشمل اليوم الوطني)")
        Console.WriteLine($"النتيجة الفعلية: {days3} أيام")
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' اختبار حساب قيمة الانتداب
    ''' </summary>
    Public Shared Sub TestAllowanceCalculation()
        Console.WriteLine("=== اختبار حساب قيمة الانتداب ===")
        
        ' اختبار قيمة الانتداب حسب المرتبة
        Dim adminRate = AssignmentCalculator.GetDailyAllowanceRate(EmployeeRank.Administrative, "الرياض")
        Dim supervisorRate = AssignmentCalculator.GetDailyAllowanceRate(EmployeeRank.Supervisor, "جدة")
        Dim managerRate = AssignmentCalculator.GetDailyAllowanceRate(EmployeeRank.Manager, "تبوك")
        Dim directorRate = AssignmentCalculator.GetDailyAllowanceRate(EmployeeRank.Director, "أخرى")
        
        Console.WriteLine($"موظف إداري في الرياض: {adminRate} ريال (متوقع: 360)")
        Console.WriteLine($"مشرف في جدة: {supervisorRate} ريال (متوقع: 480)")
        Console.WriteLine($"مدير في تبوك: {managerRate} ريال (متوقع: 650)")
        Console.WriteLine($"مدير عام في مدينة أخرى: {directorRate} ريال (متوقع: 600)")
        Console.WriteLine()
        
        ' اختبار حساب المبلغ الإجمالي
        Dim totalAmount = AssignmentCalculator.CalculateAssignmentAmount(5, 400)
        Console.WriteLine($"إجمالي مبلغ تكليف 5 أيام بمعدل 400 ريال: {totalAmount} ريال (متوقع: 2000)")
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' اختبار حساب أيام الإجازة
    ''' </summary>
    Public Shared Sub TestLeaveCalculation()
        Console.WriteLine("=== اختبار حساب أيام الإجازة ===")
        
        ' اختبار الإجازة الاعتيادية (استثناء الجمع والعطل)
        Dim startDate1 As New Date(2024, 1, 1)
        Dim endDate1 As New Date(2024, 1, 7)
        Dim annualDays = LeaveCalculator.CalculateLeaveDays(startDate1, endDate1, LeaveType.Annual)
        Console.WriteLine($"إجازة اعتيادية من {startDate1:yyyy/MM/dd} إلى {endDate1:yyyy/MM/dd}")
        Console.WriteLine($"النتيجة: {annualDays} أيام (متوقع: 5 أيام - استثناء الجمعة والسبت)")
        Console.WriteLine()
        
        ' اختبار الإجازة المرضية (جميع الأيام)
        Dim sickDays = LeaveCalculator.CalculateLeaveDays(startDate1, endDate1, LeaveType.Sick)
        Console.WriteLine($"إجازة مرضية من {startDate1:yyyy/MM/dd} إلى {endDate1:yyyy/MM/dd}")
        Console.WriteLine($"النتيجة: {sickDays} أيام (متوقع: 7 أيام - جميع الأيام)")
        Console.WriteLine()
        
        ' اختبار الإجازة الاضطرارية (جميع الأيام)
        Dim emergencyDays = LeaveCalculator.CalculateLeaveDays(startDate1, endDate1, LeaveType.Emergency)
        Console.WriteLine($"إجازة اضطرارية من {startDate1:yyyy/MM/dd} إلى {endDate1:yyyy/MM/dd}")
        Console.WriteLine($"النتيجة: {emergencyDays} أيام (متوقع: 7 أيام - جميع الأيام)")
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' اختبار التحقق من صحة الإجازة
    ''' </summary>
    Public Shared Sub TestLeaveValidation()
        Console.WriteLine("=== اختبار التحقق من صحة الإجازة ===")
        
        ' إنشاء موظف تجريبي
        Dim employee As New Employee(1, "موظف تجريبي", "محاسب", "المالية", EmployeeRank.Administrative)
        
        ' اختبار إجازة اعتيادية صالحة
        Dim validResult = LeaveCalculator.CanGrantLeave(employee, LeaveType.Annual, 10)
        Console.WriteLine($"طلب إجازة اعتيادية 10 أيام (الرصيد: {employee.AnnualLeaveBalance})")
        Console.WriteLine($"النتيجة: {If(validResult.IsValid, "صالح", "غير صالح")}")
        If validResult.Messages.Count > 0 Then
            Console.WriteLine($"الرسائل: {String.Join(", ", validResult.Messages)}")
        End If
        Console.WriteLine()
        
        ' اختبار إجازة اعتيادية غير صالحة (تتجاوز الرصيد)
        Dim invalidResult = LeaveCalculator.CanGrantLeave(employee, LeaveType.Annual, 40)
        Console.WriteLine($"طلب إجازة اعتيادية 40 يوم (الرصيد: {employee.AnnualLeaveBalance})")
        Console.WriteLine($"النتيجة: {If(invalidResult.IsValid, "صالح", "غير صالح")}")
        If invalidResult.Messages.Count > 0 Then
            Console.WriteLine($"الرسائل: {String.Join(", ", invalidResult.Messages)}")
        End If
        Console.WriteLine()
        
        ' اختبار إجازة أمومة
        Dim maternityResult = LeaveCalculator.CanGrantLeave(employee, LeaveType.Maternity, 70)
        Console.WriteLine($"طلب إجازة أمومة 70 يوم")
        Console.WriteLine($"النتيجة: {If(maternityResult.IsValid, "صالح", "غير صالح")}")
        If maternityResult.Messages.Count > 0 Then
            Console.WriteLine($"الرسائل: {String.Join(", ", maternityResult.Messages)}")
        End If
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' اختبار خصم الإجازة من الرصيد
    ''' </summary>
    Public Shared Sub TestLeaveDeduction()
        Console.WriteLine("=== اختبار خصم الإجازة من الرصيد ===")
        
        ' إنشاء موظف تجريبي
        Dim employee As New Employee(1, "موظف تجريبي", "محاسب", "المالية", EmployeeRank.Administrative)
        
        Console.WriteLine($"الرصيد الأولي للإجازة الاعتيادية: {employee.AnnualLeaveBalance}")
        
        ' خصم 10 أيام إجازة اعتيادية
        Dim deductionResult = LeaveCalculator.DeductLeaveFromBalance(employee, LeaveType.Annual, 10)
        Console.WriteLine($"خصم 10 أيام إجازة اعتيادية: {If(deductionResult, "نجح", "فشل")}")
        Console.WriteLine($"الرصيد بعد الخصم: {employee.AnnualLeaveBalance}")
        Console.WriteLine()
        
        ' محاولة خصم أكثر من الرصيد المتاح
        Dim invalidDeduction = LeaveCalculator.DeductLeaveFromBalance(employee, LeaveType.Annual, 30)
        Console.WriteLine($"محاولة خصم 30 يوم (أكثر من الرصيد): {If(invalidDeduction, "نجح", "فشل")}")
        Console.WriteLine($"الرصيد بعد المحاولة: {employee.AnnualLeaveBalance}")
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' تشغيل جميع الاختبارات
    ''' </summary>
    Public Shared Sub RunAllTests()
        Console.WriteLine("بدء تشغيل اختبارات النظام...")
        Console.WriteLine("=" * 50)
        Console.WriteLine()
        
        TestAssignmentDaysCalculation()
        TestAllowanceCalculation()
        TestLeaveCalculation()
        TestLeaveValidation()
        TestLeaveDeduction()
        
        Console.WriteLine("=" * 50)
        Console.WriteLine("انتهاء الاختبارات")
        Console.WriteLine()
        Console.WriteLine("اضغط أي مفتاح للمتابعة...")
        Console.ReadKey()
    End Sub
End Class
