''' <summary>
''' فئة التكليف - تحتوي على بيانات التكليف الرسمي
''' </summary>
Public Class Assignment
    Public Property AssignmentId As Integer
    Public Property EmployeeId As Integer
    Public Property Employee As Employee
    Public Property StartDate As Date
    Public Property EndDate As Date
    Public Property Destination As String ' وجهة التكليف
    Public Property Purpose As String ' غرض التكليف
    Public Property IsInternalAssignment As Boolean ' تكليف داخل المملكة أم خارجها
    Public Property DailyAllowance As Decimal ' قيمة الانتداب اليومي
    Public Property TotalDays As Integer ' إجمالي أيام التكليف
    Public Property TotalAmount As Decimal ' إجمالي المبلغ
    Public Property Notes As String ' ملاحظات
    Public Property CreatedDate As Date
    
    Public Sub New()
        CreatedDate = Date.Now
        IsInternalAssignment = True ' افتراضياً تكليف داخلي
    End Sub
    
    Public Sub New(employeeId As Integer, startDate As Date, endDate As Date, destination As String)
        Me.New()
        Me.EmployeeId = employeeId
        Me.StartDate = startDate
        Me.EndDate = endDate
        Me.Destination = destination
    End Sub
    
    ''' <summary>
    ''' حساب عدد أيام التكليف (باستثناء أيام الجمعة والعطل الرسمية)
    ''' </summary>
    Public Function CalculateAssignmentDays() As Integer
        If EndDate < StartDate Then
            Return 0
        End If
        
        Dim totalDays As Integer = 0
        Dim currentDate As Date = StartDate
        
        While currentDate <= EndDate
            ' استثناء يوم الجمعة
            If currentDate.DayOfWeek <> DayOfWeek.Friday Then
                ' التحقق من العطل الرسمية
                If Not IsPublicHoliday(currentDate) Then
                    totalDays += 1
                End If
            End If
            currentDate = currentDate.AddDays(1)
        End While
        
        Me.TotalDays = totalDays
        Return totalDays
    End Function
    
    ''' <summary>
    ''' حساب إجمالي مبلغ التكليف
    ''' </summary>
    Public Function CalculateTotalAmount() As Decimal
        If Employee IsNot Nothing Then
            Me.DailyAllowance = Employee.DailyAllowanceRate
        End If
        
        Dim days As Integer = CalculateAssignmentDays()
        Me.TotalAmount = days * DailyAllowance
        Return TotalAmount
    End Function
    
    ''' <summary>
    ''' التحقق من كون التاريخ عطلة رسمية
    ''' </summary>
    Private Function IsPublicHoliday(checkDate As Date) As Boolean
        ' العطل الرسمية في المملكة العربية السعودية (تواريخ تقريبية)
        ' يمكن تحديث هذه القائمة سنوياً
        Dim publicHolidays As New List(Of Date) From {
            New Date(checkDate.Year, 9, 23), ' اليوم الوطني
            New Date(checkDate.Year, 2, 22), ' يوم التأسيس
            New Date(checkDate.Year, 5, 1)   ' عيد الفطر (تاريخ تقريبي)
        }
        
        Return publicHolidays.Contains(checkDate.Date)
    End Function
End Class
