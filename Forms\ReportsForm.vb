Imports System
Imports System.Windows.Forms
Imports System.Drawing
Imports System.Diagnostics
Imports System.Text

Public Class ReportsForm
    Inherits Form
    
    Private _dataManager As DataManager
    
    Public Sub New()
        InitializeComponent()
        _dataManager = DataManager.Instance
        
        ' تعيين اتجاه النموذج من اليمين إلى اليسار
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        
        ' تعيين عنوان النموذج
        Me.Text = "التقارير"
        
        LoadEmployees()
        SetDefaultDates()
    End Sub
    
    Private Sub LoadEmployees()
        cboEmployee.DataSource = Nothing
        cboEmployee.DisplayMember = "Name"
        cboEmployee.ValueMember = "EmployeeId"
        cboEmployee.DataSource = _dataManager.Employees
    End Sub
    
    Private Sub SetDefaultDates()
        ' تعيين التواريخ الافتراضية (السنة الحالية)
        dtpFromDate.Value = New Date(Date.Now.Year, 1, 1)
        dtpToDate.Value = Date.Now
    End Sub
    
    Private Sub btnGenerateAssignmentReport_Click(sender As Object, e As EventArgs) Handles btnGenerateAssignmentReport.Click
        Try
            Dim assignments As List(Of Assignment)
            
            If cboEmployee.SelectedItem IsNot Nothing Then
                ' تقرير موظف محدد
                Dim selectedEmployee = DirectCast(cboEmployee.SelectedItem, Employee)
                assignments = _dataManager.GetAssignmentsByEmployee(selectedEmployee.EmployeeId).
                    Where(Function(a) a.StartDate >= dtpFromDate.Value AndAlso a.EndDate <= dtpToDate.Value).ToList()
            Else
                ' تقرير جميع الموظفين
                assignments = _dataManager.Assignments.
                    Where(Function(a) a.StartDate >= dtpFromDate.Value AndAlso a.EndDate <= dtpToDate.Value).ToList()
            End If
            
            If assignments.Count = 0 Then
                MessageBox.Show("لا توجد تكليفات في الفترة المحددة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            ' اختيار نوع التصدير
            Dim result = MessageBox.Show("هل تريد تصدير التقرير إلى Excel؟" & vbCrLf & "اختر نعم لـ Excel أو لا لـ PDF", 
                                       "نوع التصدير", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
            
            If result = DialogResult.Cancel Then
                Return
            End If
            
            Dim saveDialog As New SaveFileDialog()
            
            If result = DialogResult.Yes Then
                ' تصدير إلى Excel
                saveDialog.Filter = "Excel Files|*.xlsx"
                saveDialog.DefaultExt = "xlsx"
                saveDialog.FileName = $"تقرير_التكليفات_{Date.Now:yyyyMMdd}.xlsx"
                
                If saveDialog.ShowDialog() = DialogResult.OK Then
                    If ReportGenerator.ExportAssignmentsToExcel(assignments, saveDialog.FileName) Then
                        MessageBox.Show("تم تصدير التقرير بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
                        Process.Start(saveDialog.FileName)
                    Else
                        MessageBox.Show("حدث خطأ أثناء تصدير التقرير", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End If
                End If
            Else
                ' تصدير إلى PDF
                saveDialog.Filter = "PDF Files|*.pdf"
                saveDialog.DefaultExt = "pdf"
                saveDialog.FileName = $"تقرير_التكليفات_{Date.Now:yyyyMMdd}.pdf"
                
                If saveDialog.ShowDialog() = DialogResult.OK Then
                    If ReportGenerator.ExportAssignmentsToPDF(assignments, saveDialog.FileName) Then
                        MessageBox.Show("تم تصدير التقرير بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
                        Process.Start(saveDialog.FileName)
                    Else
                        MessageBox.Show("حدث خطأ أثناء تصدير التقرير", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End If
                End If
            End If
            
        Catch ex As Exception
            MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnGenerateLeaveReport_Click(sender As Object, e As EventArgs) Handles btnGenerateLeaveReport.Click
        Try
            Dim leaves As List(Of Leave)
            
            If cboEmployee.SelectedItem IsNot Nothing Then
                ' تقرير موظف محدد
                Dim selectedEmployee = DirectCast(cboEmployee.SelectedItem, Employee)
                leaves = _dataManager.GetLeavesByEmployee(selectedEmployee.EmployeeId).
                    Where(Function(l) l.StartDate >= dtpFromDate.Value AndAlso l.EndDate <= dtpToDate.Value).ToList()
            Else
                ' تقرير جميع الموظفين
                leaves = _dataManager.Leaves.
                    Where(Function(l) l.StartDate >= dtpFromDate.Value AndAlso l.EndDate <= dtpToDate.Value).ToList()
            End If
            
            If leaves.Count = 0 Then
                MessageBox.Show("لا توجد إجازات في الفترة المحددة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx"
            saveDialog.DefaultExt = "xlsx"
            saveDialog.FileName = $"تقرير_الإجازات_{Date.Now:yyyyMMdd}.xlsx"
            
            If saveDialog.ShowDialog() = DialogResult.OK Then
                If ReportGenerator.ExportLeavesToExcel(leaves, saveDialog.FileName) Then
                    MessageBox.Show("تم تصدير التقرير بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Process.Start(saveDialog.FileName)
                Else
                    MessageBox.Show("حدث خطأ أثناء تصدير التقرير", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End If
            
        Catch ex As Exception
            MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnGenerateStatisticsReport_Click(sender As Object, e As EventArgs) Handles btnGenerateStatisticsReport.Click
        Try
            If cboEmployee.SelectedItem Is Nothing Then
                MessageBox.Show("الرجاء اختيار موظف لإنشاء التقرير الإحصائي", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            
            Dim selectedEmployee = DirectCast(cboEmployee.SelectedItem, Employee)
            
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx"
            saveDialog.DefaultExt = "xlsx"
            saveDialog.FileName = $"تقرير_إحصائي_{selectedEmployee.Name}_{Date.Now:yyyyMMdd}.xlsx"
            
            If saveDialog.ShowDialog() = DialogResult.OK Then
                If ReportGenerator.GenerateEmployeeStatisticsReport(selectedEmployee, dtpFromDate.Value, dtpToDate.Value, saveDialog.FileName) Then
                    MessageBox.Show("تم إنشاء التقرير الإحصائي بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Process.Start(saveDialog.FileName)
                Else
                    MessageBox.Show("حدث خطأ أثناء إنشاء التقرير الإحصائي", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End If
            
        Catch ex As Exception
            MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnShowStatistics_Click(sender As Object, e As EventArgs) Handles btnShowStatistics.Click
        Try
            If cboEmployee.SelectedItem Is Nothing Then
                MessageBox.Show("الرجاء اختيار موظف لعرض الإحصائيات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            
            Dim selectedEmployee = DirectCast(cboEmployee.SelectedItem, Employee)
            
            ' حساب الإحصائيات
            Dim assignmentStats = AssignmentCalculator.CalculateEmployeeAssignmentStats(selectedEmployee.EmployeeId, dtpFromDate.Value, dtpToDate.Value)
            Dim leaveStats = LeaveCalculator.CalculateEmployeeLeaveStats(selectedEmployee.EmployeeId, dtpFromDate.Value, dtpToDate.Value)
            
            ' عرض الإحصائيات
            Dim statsText As New StringBuilder()
            statsText.AppendLine($"إحصائيات الموظف: {selectedEmployee.Name}")
            statsText.AppendLine($"الفترة: من {dtpFromDate.Value:yyyy/MM/dd} إلى {dtpToDate.Value:yyyy/MM/dd}")
            statsText.AppendLine()
            
            statsText.AppendLine("إحصائيات التكليفات:")
            statsText.AppendLine($"- عدد التكليفات: {assignmentStats.TotalAssignments}")
            statsText.AppendLine($"- إجمالي أيام التكليف: {assignmentStats.TotalDays}")
            statsText.AppendLine($"- إجمالي مبلغ التكليفات: {assignmentStats.TotalAmount:N2} ريال")
            statsText.AppendLine($"- التكليفات الداخلية: {assignmentStats.InternalAssignments}")
            statsText.AppendLine($"- التكليفات الخارجية: {assignmentStats.ExternalAssignments}")
            If assignmentStats.TotalAssignments > 0 Then
                statsText.AppendLine($"- متوسط أيام التكليف: {assignmentStats.AverageDaysPerAssignment:N1}")
                statsText.AppendLine($"- متوسط مبلغ التكليف: {assignmentStats.AverageAmountPerAssignment:N2} ريال")
            End If
            statsText.AppendLine()
            
            statsText.AppendLine("إحصائيات الإجازات:")
            statsText.AppendLine($"- عدد الإجازات: {leaveStats.TotalLeaves}")
            statsText.AppendLine($"- إجمالي أيام الإجازة: {leaveStats.TotalDays}")
            statsText.AppendLine($"- أيام الإجازة الاعتيادية: {leaveStats.AnnualLeaveDays}")
            statsText.AppendLine($"- أيام الإجازة المرضية: {leaveStats.SickLeaveDays}")
            statsText.AppendLine($"- أيام الإجازة الاضطرارية: {leaveStats.EmergencyLeaveDays}")
            statsText.AppendLine($"- أيام الإجازات الأخرى: {leaveStats.OtherLeaveDays}")
            If leaveStats.TotalLeaves > 0 Then
                statsText.AppendLine($"- متوسط أيام الإجازة: {leaveStats.AverageDaysPerLeave:N1}")
            End If
            statsText.AppendLine()
            
            statsText.AppendLine("أرصدة الإجازات الحالية:")
            statsText.AppendLine($"- رصيد الإجازة الاعتيادية: {selectedEmployee.AnnualLeaveBalance} يوم")
            statsText.AppendLine($"- رصيد الإجازة المرضية: {selectedEmployee.SickLeaveBalance} يوم")
            statsText.AppendLine($"- رصيد الإجازة الاضطرارية: {selectedEmployee.EmergencyLeaveBalance} يوم")
            
            txtStatistics.Text = statsText.ToString()
            
        Catch ex As Exception
            MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub
End Class
