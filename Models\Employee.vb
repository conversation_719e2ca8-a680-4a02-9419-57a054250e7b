''' <summary>
''' فئة الموظف - تحتوي على بيانات الموظف الأساسية
''' </summary>
Public Class Employee
    Public Property EmployeeId As Integer
    Public Property Name As String
    Public Property Position As String
    Public Property Department As String
    Public Property Rank As EmployeeRank
    Public Property HireDate As Date
    Public Property BasicSalary As Decimal
    Public Property DailyAllowanceRate As Decimal ' قيمة الانتداب اليومي حسب المرتبة
    
    ' رصيد الإجازات
    Public Property AnnualLeaveBalance As Integer ' رصيد الإجازة الاعتيادية
    Public Property SickLeaveBalance As Integer ' رصيد الإجازة المرضية
    Public Property EmergencyLeaveBalance As Integer ' رصيد الإجازة الاضطرارية
    
    Public Sub New()
        ' القيم الافتراضية
        AnnualLeaveBalance = 30 ' 30 يوم إجازة اعتيادية سنوياً
        SickLeaveBalance = 30 ' 30 يوم إجازة مرضية سنوياً
        EmergencyLeaveBalance = 5 ' 5 أيام إجازة اضطرارية سنوياً
    End Sub
    
    Public Sub New(id As Integer, name As String, position As String, department As String, rank As EmployeeRank)
        Me.New()
        Me.EmployeeId = id
        Me.Name = name
        Me.Position = position
        Me.Department = department
        Me.Rank = rank
        Me.DailyAllowanceRate = GetDailyAllowanceByRank(rank)
    End Sub
    
    ''' <summary>
    ''' حساب قيمة الانتداب اليومي حسب المرتبة
    ''' </summary>
    Private Function GetDailyAllowanceByRank(rank As EmployeeRank) As Decimal
        Select Case rank
            Case EmployeeRank.Administrative
                Return 300D ' 300 ريال يومياً
            Case EmployeeRank.Supervisor
                Return 400D ' 400 ريال يومياً
            Case EmployeeRank.Manager
                Return 500D ' 500 ريال يومياً
            Case EmployeeRank.Director
                Return 600D ' 600 ريال يومياً
            Case Else
                Return 250D ' القيمة الافتراضية
        End Select
    End Function
End Class

''' <summary>
''' تعداد مراتب الموظفين
''' </summary>
Public Enum EmployeeRank
    Administrative = 1 ' موظف إداري
    Supervisor = 2 ' مشرف
    Manager = 3 ' مدير
    Director = 4 ' مدير عام
End Enum
