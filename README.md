# نظام حسابات الموظفين في المهام الرسمية

## وصف البرنامج
برنامج شامل لإدارة وحساب التكليفات والإجازات للموظفين في المؤسسات الحكومية بالمملكة العربية السعودية. يوفر البرنامج حسابات دقيقة لأيام التكليف ومبالغ الانتداب، بالإضافة إلى إدارة أنواع الإجازات المختلفة وأرصدتها.

## المميزات الرئيسية

### 1. إدارة التكليفات
- حساب عدد أيام التكليف مع استثناء العطل الرسمية وأيام الجمعة
- حساب قيمة الانتداب اليومي حسب مرتبة الموظف والمدينة
- دعم التكليفات الداخلية والخارجية
- حفظ تفاصيل التكليف والملاحظات

### 2. إدارة الإجازات
- دعم أنواع الإجازات المختلفة:
  - الإجازة الاعتيادية (30 يوم سنوياً)
  - الإجازة المرضية (30 يوم سنوياً)
  - الإجازة الاضطرارية (5 أيام سنوياً)
  - إجازة الأمومة (70 يوم)
  - إجازة الأبوة (3 أيام)
  - إجازة الحج (21 يوم - مرة واحدة في الخدمة)
- حساب أيام الإجازة حسب نوعها
- إدارة أرصدة الإجازات وخصم الأيام المستخدمة
- نظام اعتماد الإجازات

### 3. التقارير والإحصائيات
- تقارير التكليفات (CSV/PDF)
- تقارير الإجازات (CSV)
- تقارير إحصائية شاملة للموظفين
- إحصائيات مفصلة لكل موظف

### 4. واجهة المستخدم
- واجهة باللغة العربية مع دعم الاتجاه من اليمين إلى اليسار
- تصميم بسيط وسهل الاستخدام
- تبويبات منفصلة للتكليفات والإجازات والتقارير

## متطلبات النظام
- Windows 10 أو أحدث
- .NET 6.0 أو أحدث
- Visual Studio 2022 (للتطوير)

## طريقة التشغيل

### 1. تشغيل البرنامج من الكود المصدري
```bash
# فتح مجلد المشروع
cd EmployeeAssignmentSystem

# بناء المشروع
dotnet build

# تشغيل البرنامج
dotnet run
```

### 2. استخدام البرنامج

#### إدارة الموظفين
1. اختر الموظف من القائمة المنسدلة في أعلى الشاشة
2. ستظهر معلومات الموظف وأرصدة الإجازات

#### إضافة تكليف جديد
1. انتقل إلى تبويب "التكليفات"
2. أدخل تاريخ البداية والنهاية
3. أدخل وجهة التكليف والغرض
4. حدد نوع التكليف (داخلي/خارجي)
5. اضغط "حساب" لمعاينة النتيجة
6. اضغط "حفظ" لحفظ التكليف

#### إضافة إجازة جديدة
1. انتقل إلى تبويب "الإجازات"
2. أدخل تاريخ البداية والنهاية
3. اختر نوع الإجازة
4. أدخل سبب الإجازة
5. اضغط "حساب" لمعاينة النتيجة والتحقق من الرصيد
6. اضغط "حفظ" لحفظ الإجازة

#### إنشاء التقارير
1. انتقل إلى تبويب "التقارير"
2. اختر الموظف (أو اتركه فارغاً لجميع الموظفين)
3. حدد فترة التقرير
4. اختر نوع التقرير المطلوب
5. اضغط الزر المناسب لإنشاء التقرير

## هيكل المشروع

```
EmployeeAssignmentSystem/
├── Models/                 # نماذج البيانات
│   ├── Employee.vb        # فئة الموظف
│   ├── Assignment.vb      # فئة التكليف
│   ├── Leave.vb          # فئة الإجازة
│   └── DataManager.vb    # مدير البيانات
├── Services/              # الخدمات
│   ├── AssignmentCalculator.vb  # حاسبة التكليفات
│   ├── LeaveCalculator.vb       # حاسبة الإجازات
│   └── ReportGenerator.vb       # مولد التقارير
├── Forms/                 # النماذج
│   ├── MainForm.vb       # النموذج الرئيسي
│   ├── MainForm.Designer.vb
│   └── ReportsForm.vb    # نموذج التقارير
├── My Project/           # ملفات المشروع
└── Program.vb           # نقطة الدخول الرئيسية
```

## قواعد الحساب

### حساب أيام التكليف
- يتم استثناء أيام الجمعة من حساب أيام التكليف
- يتم استثناء العطل الرسمية (اليوم الوطني، يوم التأسيس، عيد الفطر، عيد الأضحى)
- يمكن تخصيص قائمة العطل الرسمية سنوياً

### حساب قيمة الانتداب
- موظف إداري: 300 ريال/يوم
- مشرف: 400 ريال/يوم
- مدير: 500 ريال/يوم
- مدير عام: 600 ريال/يوم

### معاملات المدن
- المدن الكبرى (الرياض، جدة، الدمام، مكة، المدينة): زيادة 20%
- المدن النائية (تبوك، أبها، جازان، نجران، الباحة، عرعر، سكاكا): زيادة 30%

### حساب أيام الإجازة
- **الإجازة الاعتيادية**: استثناء الجمع والعطل الرسمية
- **الإجازة المرضية**: حساب جميع الأيام
- **الإجازة الاضطرارية**: حساب جميع الأيام
- **إجازة الأمومة**: حساب جميع الأيام (70 يوم كحد أقصى)
- **إجازة الأبوة**: استثناء الجمع والعطل الرسمية (3 أيام كحد أقصى)
- **إجازة الحج**: حساب جميع الأيام (21 يوم، مرة واحدة في الخدمة)

## البيانات التجريبية
يحتوي البرنامج على بيانات تجريبية لثلاثة موظفين:
1. أحمد محمد - محاسب - المالية
2. فاطمة علي - مشرفة - الموارد البشرية
3. محمد سالم - مدير - تقنية المعلومات

## التطوير المستقبلي
- إضافة قاعدة بيانات حقيقية (SQL Server/SQLite)
- دعم مكتبات Excel و PDF الكاملة
- إضافة نظام المستخدمين والصلاحيات
- إضافة المزيد من أنواع التقارير
- دعم النسخ الاحتياطي واستعادة البيانات
- إضافة إشعارات انتهاء صلاحية الإجازات

## المساهمة
يمكن المساهمة في تطوير البرنامج من خلال:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود الموجود
- إضافة اختبارات جديدة

## الترخيص
هذا البرنامج مفتوح المصدر ومتاح للاستخدام والتطوير.

## الدعم
للحصول على الدعم أو الاستفسارات، يرجى التواصل مع فريق التطوير.
