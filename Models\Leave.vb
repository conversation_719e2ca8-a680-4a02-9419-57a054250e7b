''' <summary>
''' فئة الإجازة - تحتوي على بيانات الإجازات
''' </summary>
Public Class Leave
    Public Property LeaveId As Integer
    Public Property EmployeeId As Integer
    Public Property Employee As Employee
    Public Property StartDate As Date
    Public Property EndDate As Date
    Public Property LeaveType As LeaveType
    Public Property TotalDays As Integer
    Public Property Reason As String ' سبب الإجازة
    Public Property IsApproved As Boolean ' هل الإجازة معتمدة
    Public Property ApprovedBy As String ' معتمدة من قبل
    Public Property ApprovalDate As Date?
    Public Property Notes As String
    Public Property CreatedDate As Date
    
    Public Sub New()
        CreatedDate = Date.Now
        IsApproved = False
    End Sub
    
    Public Sub New(employeeId As Integer, startDate As Date, endDate As Date, leaveType As LeaveType)
        Me.New()
        Me.EmployeeId = employeeId
        Me.StartDate = startDate
        Me.EndDate = endDate
        Me.LeaveType = leaveType
    End Sub
    
    ''' <summary>
    ''' حساب عدد أيام الإجازة (باستثناء أيام الجمعة والعطل الرسمية)
    ''' </summary>
    Public Function CalculateLeaveDays() As Integer
        If EndDate < StartDate Then
            Return 0
        End If
        
        Dim totalDays As Integer = 0
        Dim currentDate As Date = StartDate
        
        While currentDate <= EndDate
            ' استثناء يوم الجمعة للإجازة الاعتيادية فقط
            If LeaveType = LeaveType.Annual Then
                If currentDate.DayOfWeek <> DayOfWeek.Friday Then
                    If Not IsPublicHoliday(currentDate) Then
                        totalDays += 1
                    End If
                End If
            Else
                ' الإجازة المرضية والاضطرارية تحسب جميع الأيام
                totalDays += 1
            End If
            currentDate = currentDate.AddDays(1)
        End While
        
        Me.TotalDays = totalDays
        Return totalDays
    End Function
    
    ''' <summary>
    ''' التحقق من إمكانية منح الإجازة حسب الرصيد المتاح
    ''' </summary>
    Public Function CanGrantLeave() As Boolean
        If Employee Is Nothing Then
            Return False
        End If
        
        Dim requestedDays As Integer = CalculateLeaveDays()
        
        Select Case LeaveType
            Case LeaveType.Annual
                Return Employee.AnnualLeaveBalance >= requestedDays
            Case LeaveType.Sick
                Return Employee.SickLeaveBalance >= requestedDays
            Case LeaveType.Emergency
                Return Employee.EmergencyLeaveBalance >= requestedDays
            Case Else
                Return False
        End Select
    End Function
    
    ''' <summary>
    ''' خصم أيام الإجازة من رصيد الموظف
    ''' </summary>
    Public Sub DeductFromBalance()
        If Employee Is Nothing OrElse Not IsApproved Then
            Return
        End If
        
        Dim daysToDeduct As Integer = CalculateLeaveDays()
        
        Select Case LeaveType
            Case LeaveType.Annual
                Employee.AnnualLeaveBalance -= daysToDeduct
            Case LeaveType.Sick
                Employee.SickLeaveBalance -= daysToDeduct
            Case LeaveType.Emergency
                Employee.EmergencyLeaveBalance -= daysToDeduct
        End Select
    End Sub
    
    ''' <summary>
    ''' التحقق من كون التاريخ عطلة رسمية
    ''' </summary>
    Private Function IsPublicHoliday(checkDate As Date) As Boolean
        Dim publicHolidays As New List(Of Date) From {
            New Date(checkDate.Year, 9, 23), ' اليوم الوطني
            New Date(checkDate.Year, 2, 22), ' يوم التأسيس
            New Date(checkDate.Year, 5, 1)   ' عيد الفطر (تاريخ تقريبي)
        }
        
        Return publicHolidays.Contains(checkDate.Date)
    End Function
End Class

''' <summary>
''' تعداد أنواع الإجازات
''' </summary>
Public Enum LeaveType
    Annual = 1 ' إجازة اعتيادية
    Sick = 2 ' إجازة مرضية
    Emergency = 3 ' إجازة اضطرارية
    Maternity = 4 ' إجازة أمومة
    Paternity = 5 ' إجازة أبوة
    Hajj = 6 ' إجازة حج
End Enum
